import ast

import astor

enum_class = ast.ClassDef(
    name="Status",
    bases=[ast.Name(id="Enum", ctx=ast.Load())],
    keywords=[],
    body=[
        ast.Assign(
            targets=[ast.Name(id="EVENT", ctx=ast.Store())],
            value=ast.Constant("event"),
        ),
        ast.Assign(
            targets=[ast.Name(id="EVENT_NUTRITION_FOOD_MEAT_BEEF", ctx=ast.Store())],
            value=ast.Constant("event.nutrition.food.meat.beef"),
        ),
    ],
    decorator_list=[],
)

module = ast.Module(
    body=[
        ast.ImportFrom(module="enum", names=[ast.alias(name="Enum")], level=0),
        enum_class,
    ],
    type_ignores=[],
)

with open("status_enum.py", "w") as f:
    f.write(astor.to_source(module))
