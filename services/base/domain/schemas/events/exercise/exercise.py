from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class ExerciseFields(EventFields):
    RATING = "rating"


class ExerciseCategory(StrEnum):
    STATIC_BALANCE = "static_balance"
    DYNAMIC_BALANCE = "dynamic_balance"
    STABILITY = "stability"
    DAILY_ACTIVITIES = "daily_activities"
    REHABILITATION = "rehabilitation"
    INJURY_PREVENTION = "injury_prevention"
    STATIC_STRETCHING = "static_stretching"
    DYNAMIC_STRETCHING = "dynamic_stretching"
    JOINT_MOBILITY = "joint_mobility"
    MUSCLE_ACTIVATION = "muscle_activation"
    SELF_MASSAGE = "self_massage"
    SWORD_ARTS = "sword_arts"
    BASKETBALL = "basketball"
    VOLLEYBALL = "volleyball"
    TENNIS = "tennis"
    PICKLEBALL = "pickleball"
    BADMINTON = "badminton"
    SQUASH = "squash"
    SOCCER = "soccer"
    ULTIMATE_FRISBEE = "ultimate_frisbee"
    RUGBY = "rugby"
    AMERICAN_FOOTBALL = "american_football"
    FLAG_FOOTBALL = "flag_football"
    ARCHERY = "archery"
    BOWLING = "bowling"
    TABLE_TENNIS = "table_tennis"
    GOLF = "golf"
    CRICKET = "cricket"
    LACROSSE = "lacrosse"
    FIELD_HOCKEY = "field_hockey"
    HANDBALL = "handball"
    BOXING = "boxing"
    KICKBOXING = "kickboxing"
    WRESTLING = "wrestling"
    JUDO = "judo"
    SPORT_OTHER = "sport_other"
    EXERCISE = "exercise"


class ExerciseIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Exercise


class Exercise(Event, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(
        alias=ExerciseFields.CATEGORY,
    )
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
