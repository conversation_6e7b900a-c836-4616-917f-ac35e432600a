from collections import defaultdict
from functools import lru_cache
from typing import Dict, Iterator, List, Sequence, Set, Union

from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.opensearch_index_constants import COLLECTIONS


class TreeNode:
    def __init__(self, path: str) -> None:
        split = path.split(".")
        self._path = path
        self._name = split[-1]
        self._parent = split[:-1] if len(split) > 1 else None
        self._type = self._get_type(split=split)
        self._collection = self._get_collection(split=split)

    def _get_type(self, split: Sequence[str]) -> DataType:
        for s in split:
            if s in DataType:
                return DataType(s)
        raise ShouldNotReachHereException(f"Type not found in path: {split}")

    def _get_collection(self, split: Sequence[str]) -> str | None:
        for s in split:
            if s in COLLECTIONS:
                return s
        return None


class TypeTree:
    __slots__ = ("_children", "_parent", "_roots", "_all_nodes")

    def __init__(self) -> None:
        self._children = defaultdict(set)
        self._parent = {}
        self._roots = set()
        self._all_nodes = set()

    @classmethod
    def from_list(cls, paths: List[str]) -> "TypeTree":
        """
        Create a TypeTree from a list of dot-separated paths.

        Args:
            paths: List of dot-separated paths like ["event", "event.nutrition", "event.nutrition.food"]
        """
        if not isinstance(paths, list):
            raise TypeError("paths must be a list")
        if not paths:
            raise ValueError("paths cannot be empty")

        instance = cls()
        instance._build_from_list(paths)
        return instance

    def parent(self, node: str) -> str | None:
        return self._parent.get(node)

    def children(self, node: str) -> Sequence[str]:
        return sorted(self._children.get(node, ()))

    def root(self) -> str | None:
        """Get the single root node, or None if there are multiple roots."""
        return next(iter(self._roots)) if len(self._roots) == 1 else None

    def roots(self) -> Sequence[str]:
        return sorted(self._roots)

    def contains(self, node_or_path: Union[str, List[str]]) -> bool:
        """
        Check if node or path exists in the tree.

        Args:
            node_or_path: Either a node name (str) or a path from root (list[str])
        """
        assert node_or_path
        if isinstance(node_or_path, str):
            return node_or_path in self._all_nodes
        elif isinstance(node_or_path, list):
            for node in node_or_path:
                assert node
                if node not in self._all_nodes:
                    return False

            # Check if the path is actually valid (each node is parent of next)
            for i in range(len(node_or_path) - 1):
                parent = node_or_path[i]
                child = node_or_path[i + 1]
                if child not in self._children.get(parent, set()):
                    return False

            # Check if first node is a root
            return node_or_path[0] in self._roots
        else:
            raise TypeError("node_or_path must be a string or list of strings")

    def is_leaf(self, node: str) -> bool:
        return len(self._children.get(node, ())) == 0

    def is_root(self, node: str) -> bool:
        return node in self._roots

    def depth(self, node: str) -> int | None:
        path = self.path_from_root(node)
        return len(path) - 1 if path else None

    def siblings(self, node: str) -> List[str]:
        parent = self.parent(node)
        if parent is None:
            return []
        return [child for child in self.children(parent) if child != node]

    @lru_cache(maxsize=None)
    def path_from_root(self, node: str) -> List[str] | None:
        if not self.contains(node):
            return None
        path = [node]
        cur = node
        while cur in self._parent:
            cur = self._parent[cur]
            path.append(cur)
        path.reverse()
        return path

    def get_children_paths(
        self, node: str, leaves_only: bool, from_root: bool = True, max_depth: int | None = None
    ) -> List[List[str]] | None:
        """
        Return list of paths (each a list[str]) to descendants of `node`.

        Args:
          node: start node
          from_root: if True, paths begin at the tree's root (or the top of
                     the forest if multiple roots). If False, paths begin at `node`.
          leaves_only: if True, only include paths that end at leaves
                       (nodes with no children). If False, include paths to all
                       descendants encountered (including internal nodes).
          max_depth: limit on edges from `node` to the end of each path.
                     None means unlimited; 1 means immediate children.

        """
        if not self.contains(node):
            return None

        base_from_root = self.path_from_root(node) if from_root else [node]
        if not self._children.get(node):
            return []

        paths = self.dfs(node, collect_paths=True, max_depth=max_depth, leaves_only=leaves_only)

        if from_root:
            prefix = base_from_root[:-1]  # everything up to (but not including) `node`
            return [prefix + rel for rel in paths]
        else:
            return paths

    def __iter__(self) -> Iterator[str]:
        """Iterate over all nodes in the tree."""
        return iter(self._all_nodes)

    def dfs(
        self, node: str, collect_paths: bool = False, max_depth: int | None = None, leaves_only: bool = False
    ) -> Union[List[str], List[List[str]]]:
        """
        Depth-first search traversal of the tree.

        Args:
            node: Node to start traversal from
            collect_paths: If True, collect and return paths instead of nodes
            max_depth: Maximum depth to traverse (None for unlimited)
            leaves_only: If True with collect_paths, only collect paths to leaves

        Returns:
            If collect_paths=True: List of paths (each path is a list of strings)
            If collect_paths=False: List of node names
        """
        if not self.contains(node):
            return []

        if collect_paths:
            paths = []

            def collect_dfs(cur, path, depth):
                # For leaves_only mode, only add paths that end at leaves
                # For non-leaves_only mode, add all paths except the start node itself
                if cur != node:
                    if (not leaves_only) or self.is_leaf(cur):
                        paths.append(path[:])

                # Stop recursion if we've reached max_depth
                if max_depth is not None and depth >= max_depth:
                    return

                for child in sorted(self.children(cur)):
                    collect_dfs(child, path + [child], depth + 1)

            collect_dfs(node, [node], 0)
            return paths

        else:
            nodes = []
            stack = [(node, 0)]
            visited = set()

            while stack:
                node, depth = stack.pop()
                if node not in visited:
                    visited.add(node)
                    nodes.append(node)

                    # Add children to stack if we haven't reached max_depth
                    if max_depth is None or depth < max_depth:
                        children = list(reversed(sorted(self.children(node))))
                        for child in children:
                            stack.append((child, depth + 1))

            return nodes

    def _build_from_list(self, paths: List[str]) -> None:
        """Build tree structure from a list of dot-separated paths."""

        def add_edge(p, c):
            self._children[p].add(c)
            self._parent[c] = p
            self._children.setdefault(c, set())
            self._roots.discard(c)
            self._all_nodes.add(p)
            self._all_nodes.add(c)

        def ensure_root(n):
            self._children.setdefault(n, set())
            self._all_nodes.add(n)
            if n not in self._parent:
                self._roots.add(n)

        # Process each path
        for path in paths:
            if not isinstance(path, str):
                raise TypeError(f"All paths must be strings, got {type(path)}")
            if not path:
                raise ValueError("Empty path not allowed")

            # Split the path into components
            components = path.split(".")

            # Ensure the root exists
            root = components[0]
            ensure_root(root)

            # Add edges for each parent-child relationship in the path
            for i in range(len(components) - 1):
                parent = components[i]
                child = components[i + 1]
                add_edge(parent, child)


if __name__ == "__main__":
    # t = TypeTree.from_dict(
    #     {
    #         "doc": {
    #             "event": {
    #                 "exercise": {
    #                     "cardio": {"running", "cycling"},
    #                     "strength": {"weights", "yoga"},
    #                     "static_balance": {},
    #                     "dynamic_balance": {},
    #                     "stability": {},
    #                     "daily_activities": {},
    #                     "rehabilitation": {},
    #                     "injury_prevention": {},
    #                     "static_stretching": {},
    #                     "dynamic_stretching": {},
    #                     "joint_mobility": {},
    #                     "muscle_activation": {},
    #                     "self_massage": {},
    #                     "sword_arts": {},
    #                     "basketball": {},
    #                     "volleyball": {},
    #                     "tennis": {},
    #                     "pickleball": {},
    #                     "badminton": {},
    #                     "squash": {},
    #                     "soccer": {},
    #                     "ultimate_frisbee": {},
    #                     "rugby": {},
    #                     "american_football": {},
    #                     "flag_football": {},
    #                     "archery": {},
    #                     "bowling": {},
    #                     "table_tennis": {},
    #                     "golf": {},
    #                     "cricket": {},
    #                     "lacrosse": {},
    #                     "field_hockey": {},
    #                     "handball": {},
    #                     "boxing": {},
    #                     "kickboxing": {},
    #                     "wrestling": {},
    #                     "judo": {},
    #                     "sport_other": {},
    #                 },
    #                 "content": {
    #                     "movie",
    #                     "series",
    #                     "sport",
    #                     "game",
    #                     "social",
    #                     "music",
    #                     "podcast",
    #                     "audiobook",
    #                     "book",
    #                     "article",
    #                     "news",
    #                     "short",
    #                 },
    #             }
    #         }
    #     }
    # )
    t = TypeTree.from_list([
            "activity.app.productivity",
            "activity.app.interactive.augmented_reality",

            "content.movie",
            "content.series",
            "content.sport",
            "content.game",
            "content.social",
            "content.music",
            "content.podcast",
            "content.audiobook",
            "content.book",
            "content.article",
            "content.news",
            "content.short",

            "exercise.cardio.running",
            "exercise.cardio.cycling",
            "exercise.strength.weights",
            "exercise.strength.yoga",
            "exercise.static_balance",
            "exercise.dynamic_balance",
            "exercise.stability",
            "exercise.daily_activities",
            "exercise.rehabilitation",
            "exercise.injury_prevention",
            "exercise.static_stretching",
            "exercise.dynamic_stretching",
            "exercise.joint_mobility",
            "exercise.muscle_activation",
            "exercise.self_massage",
            "exercise.sword_arts",
            "exercise.basketball",
            "exercise.volleyball",
            "exercise.tennis",
            "exercise.pickleball",
            "exercise.badminton",
            "exercise.squash",
            "exercise.soccer",
            "exercise.ultimate_frisbee",
            "exercise.rugby",
            "exercise.american_football",
            "exercise.flag_football",
            "exercise.archery",
            "exercise.bowling",
            "exercise.table_tennis",
            "exercise.golf",
            "exercise.cricket",
            "exercise.lacrosse",
            "exercise.field_hockey",
            "exercise.handball",
            "exercise.boxing",
            "exercise.kickboxing",
            "exercise.wrestling",
            "exercise.judo",

            "nutrition.food.meat",
            "nutrition.drink.milk",


        ]
    )
